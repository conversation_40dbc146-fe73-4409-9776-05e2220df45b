import { getRequestConfig } from 'next-intl/server';
import { locales, type Locale } from './config';

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as Locale)) {
    // Return default locale messages if invalid
    return {
      messages: (await import('./locales/en.json')).default
    };
  }

  return {
    messages: (await import(`./locales/${locale}.json`)).default
  };
});

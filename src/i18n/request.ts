import { getRequestConfig } from 'next-intl/server';
import { locales, type Locale, defaultLocale } from './config';
import { notFound } from 'next/navigation';

export default getRequestConfig(async ({ locale }) => {
  // <PERSON>le undefined locale by using default
  if (!locale) {
    locale = defaultLocale;
  }

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  return {
    locale,
    messages: (await import(`./locales/${locale}.json`)).default
  };
});

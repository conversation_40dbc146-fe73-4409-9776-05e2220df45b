import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';

// Can be imported from a shared config
export const locales = ['en', 'fr', 'de', 'ar', 'zh', 'ja', 'ru', 'es'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'en';

// Language configuration with metadata
export const languages = {
  en: { 
    name: 'English', 
    nativeName: 'English', 
    flag: '🇺🇸', 
    dir: 'ltr' as const,
    currency: 'USD',
    dateFormat: 'MM/dd/yyyy'
  },
  fr: { 
    name: 'French', 
    nativeName: 'Français', 
    flag: '🇫🇷', 
    dir: 'ltr' as const,
    currency: 'EUR',
    dateFormat: 'dd/MM/yyyy'
  },
  de: { 
    name: 'German', 
    nativeName: 'Deutsch', 
    flag: '🇩🇪', 
    dir: 'ltr' as const,
    currency: 'EUR',
    dateFormat: 'dd.MM.yyyy'
  },
  ar: { 
    name: 'Arabic', 
    nativeName: 'العربية', 
    flag: '🇸🇦', 
    dir: 'rtl' as const,
    currency: 'SAR',
    dateFormat: 'dd/MM/yyyy'
  },
  zh: { 
    name: 'Chinese', 
    nativeName: '中文', 
    flag: '🇨🇳', 
    dir: 'ltr' as const,
    currency: 'CNY',
    dateFormat: 'yyyy/MM/dd'
  },
  ja: { 
    name: 'Japanese', 
    nativeName: '日本語', 
    flag: '🇯🇵', 
    dir: 'ltr' as const,
    currency: 'JPY',
    dateFormat: 'yyyy/MM/dd'
  },
  ru: { 
    name: 'Russian', 
    nativeName: 'Русский', 
    flag: '🇷🇺', 
    dir: 'ltr' as const,
    currency: 'RUB',
    dateFormat: 'dd.MM.yyyy'
  },
  es: { 
    name: 'Spanish', 
    nativeName: 'Español', 
    flag: '🇪🇸', 
    dir: 'ltr' as const,
    currency: 'EUR',
    dateFormat: 'dd/MM/yyyy'
  }
} as const;

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as Locale)) notFound();

  return {
    messages: (await import(`./locales/${locale}.json`)).default
  };
});

// Helper functions
export function getLanguageInfo(locale: Locale) {
  return languages[locale];
}

export function isRTL(locale: Locale): boolean {
  return languages[locale].dir === 'rtl';
}

export function getCurrencyForLocale(locale: Locale): string {
  return languages[locale].currency;
}

export function getDateFormatForLocale(locale: Locale): string {
  return languages[locale].dateFormat;
}

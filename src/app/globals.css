@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* Light mode - Modern tech color scheme */
:root {
  --radius: 0.625rem;
  
  /* Main backgrounds and text */
  --background: oklch(0.99 0.005 240);
  --foreground: oklch(0.15 0.02 240);
  
  /* Card and surface colors */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 240);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 240);
  
  /* Primary colors - Electric blue theme */
  --primary: oklch(0.55 0.25 250);
  --primary-foreground: oklch(0.98 0.01 250);
  
  /* Secondary colors - Cool gray */
  --secondary: oklch(0.95 0.01 240);
  --secondary-foreground: oklch(0.25 0.02 240);
  
  /* Muted colors */
  --muted: oklch(0.96 0.01 240);
  --muted-foreground: oklch(0.45 0.02 240);
  
  /* Accent colors - Cyan accent */
  --accent: oklch(0.92 0.05 200);
  --accent-foreground: oklch(0.2 0.02 200);
  
  /* Destructive colors - Modern red */
  --destructive: oklch(0.6 0.22 25);
  --destructive-foreground: oklch(0.98 0.01 25);
  
  /* Border and input colors */
  --border: oklch(0.9 0.01 240);
  --input: oklch(0.94 0.01 240);
  --ring: oklch(0.55 0.25 250);
  
  /* Chart colors - Vibrant tech palette */
  --chart-1: oklch(0.6 0.25 250);  /* Electric blue */
  --chart-2: oklch(0.65 0.2 180);  /* Cyan */
  --chart-3: oklch(0.7 0.25 120);  /* Green */
  --chart-4: oklch(0.65 0.25 60);  /* Yellow */
  --chart-5: oklch(0.6 0.25 320);  /* Purple */
  
  /* Sidebar colors */
  --sidebar: oklch(0.98 0.005 240);
  --sidebar-foreground: oklch(0.15 0.02 240);
  --sidebar-primary: oklch(0.55 0.25 250);
  --sidebar-primary-foreground: oklch(0.98 0.01 250);
  --sidebar-accent: oklch(0.95 0.01 240);
  --sidebar-accent-foreground: oklch(0.25 0.02 240);
  --sidebar-border: oklch(0.9 0.01 240);
  --sidebar-ring: oklch(0.55 0.25 250);
}

/* Dark mode - Enhanced dark theme */
.dark {
  /* Main backgrounds and text */
  --background: oklch(0.08 0.01 240);
  --foreground: oklch(0.95 0.01 240);
  
  /* Card and surface colors */
  --card: oklch(0.12 0.02 240);
  --card-foreground: oklch(0.95 0.01 240);
  --popover: oklch(0.12 0.02 240);
  --popover-foreground: oklch(0.95 0.01 240);
  
  /* Primary colors - Brighter blue for dark mode */
  --primary: oklch(0.7 0.25 250);
  --primary-foreground: oklch(0.08 0.01 240);
  
  /* Secondary colors */
  --secondary: oklch(0.18 0.02 240);
  --secondary-foreground: oklch(0.9 0.01 240);
  
  /* Muted colors */
  --muted: oklch(0.15 0.02 240);
  --muted-foreground: oklch(0.65 0.01 240);
  
  /* Accent colors */
  --accent: oklch(0.2 0.05 200);
  --accent-foreground: oklch(0.9 0.01 200);
  
  /* Destructive colors */
  --destructive: oklch(0.7 0.25 25);
  --destructive-foreground: oklch(0.08 0.01 25);
  
  /* Border and input colors */
  --border: oklch(0.2 0.02 240);
  --input: oklch(0.16 0.02 240);
  --ring: oklch(0.7 0.25 250);
  
  /* Chart colors - Adjusted for dark mode */
  --chart-1: oklch(0.7 0.25 250);   /* Bright blue */
  --chart-2: oklch(0.75 0.2 180);   /* Bright cyan */
  --chart-3: oklch(0.8 0.25 120);   /* Bright green */
  --chart-4: oklch(0.8 0.25 60);    /* Bright yellow */
  --chart-5: oklch(0.75 0.25 320);  /* Bright purple */
  
  /* Sidebar colors */
  --sidebar: oklch(0.1 0.01 240);
  --sidebar-foreground: oklch(0.95 0.01 240);
  --sidebar-primary: oklch(0.7 0.25 250);
  --sidebar-primary-foreground: oklch(0.08 0.01 240);
  --sidebar-accent: oklch(0.18 0.02 240);
  --sidebar-accent-foreground: oklch(0.9 0.01 240);
  --sidebar-border: oklch(0.2 0.02 240);
  --sidebar-ring: oklch(0.7 0.25 250);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground transition-colors duration-300;
  }
  
  /* Improved dark mode transitions */
  html {
    @apply transition-colors duration-300;
  }
}

/* Custom animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce-gentle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px oklch(0.55 0.25 250 / 0.5);
  }
  50% {
    box-shadow: 0 0 20px oklch(0.55 0.25 250 / 0.8);
  }
}

/* Animation classes */
.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
  opacity: 0;
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out forwards;
}

.animate-bounce-gentle {
  animation: bounce-gentle 2s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px oklch(0.15 0.02 240 / 0.1);
}

.dark .hover-lift:hover {
  box-shadow: 0 10px 25px oklch(0.05 0.01 240 / 0.3);
}

/* Gradient text - Updated colors */
.gradient-text {
  background: linear-gradient(135deg, oklch(0.55 0.25 250) 0%, oklch(0.65 0.2 180) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dark .gradient-text {
  background: linear-gradient(135deg, oklch(0.7 0.25 250) 0%, oklch(0.75 0.2 180) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Custom scrollbar - Updated for new theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: oklch(0.96 0.01 240);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, oklch(0.55 0.25 250) 0%, oklch(0.65 0.2 180) 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, oklch(0.5 0.25 250) 0%, oklch(0.6 0.2 180) 100%);
}

.dark ::-webkit-scrollbar-track {
  background: oklch(0.15 0.02 240);
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, oklch(0.7 0.25 250) 0%, oklch(0.75 0.2 180) 100%);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, oklch(0.75 0.25 250) 0%, oklch(0.8 0.2 180) 100%);
}

/* Loading skeleton */
.skeleton {
  background: linear-gradient(90deg, oklch(0.94 0.01 240) 25%, oklch(0.9 0.01 240) 50%, oklch(0.94 0.01 240) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Glass morphism effect */
.glass {
  background: oklch(1 0 0 / 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid oklch(1 0 0 / 0.2);
}

/* Responsive grid improvements */
@media (max-width: 768px) {
  .animate-fade-in-up {
    animation-delay: 0ms !important;
  }
}

/* Dark mode enhancements */
.dark .skeleton {
  background: linear-gradient(90deg, oklch(0.18 0.02 240) 25%, oklch(0.22 0.02 240) 50%, oklch(0.18 0.02 240) 75%);
  background-size: 200% 100%;
}

.dark .glass {
  background: oklch(0.08 0.01 240 / 0.2);
  border: 1px solid oklch(0.95 0.01 240 / 0.1);
}

/* Smooth transitions for all interactive elements */
* {
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible,
select:focus-visible {
  outline: 2px solid oklch(0.55 0.25 250);
  outline-offset: 2px;
}

.dark button:focus-visible,
.dark input:focus-visible,
.dark select:focus-visible {
  outline: 2px solid oklch(0.7 0.25 250);
  outline-offset: 2px;
}

/* Enhanced button styles for better dark mode support */
.btn-primary {
  background: linear-gradient(135deg, oklch(0.55 0.25 250) 0%, oklch(0.65 0.2 180) 100%);
  color: oklch(0.98 0.01 250);
  border: none;
  transition: all 0.2s ease-in-out;
}

.btn-primary:hover {
  background: linear-gradient(135deg, oklch(0.5 0.25 250) 0%, oklch(0.6 0.2 180) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px oklch(0.55 0.25 250 / 0.3);
}

.dark .btn-primary {
  background: linear-gradient(135deg, oklch(0.7 0.25 250) 0%, oklch(0.75 0.2 180) 100%);
  color: oklch(0.08 0.01 240);
}

.dark .btn-primary:hover {
  background: linear-gradient(135deg, oklch(0.75 0.25 250) 0%, oklch(0.8 0.2 180) 100%);
  box-shadow: 0 4px 12px oklch(0.7 0.25 250 / 0.4);
}


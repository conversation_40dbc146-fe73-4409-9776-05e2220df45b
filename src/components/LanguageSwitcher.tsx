'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useLocale, useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Globe, ChevronDown } from 'lucide-react';
import { languages, type Locale } from '@/i18n/config';

export function LanguageSwitcher() {
  const t = useTranslations('navigation');
  const router = useRouter();
  const pathname = usePathname();
  const hookLocale = useLocale() as Locale;
  const [isOpen, setIsOpen] = useState(false);

  // Fallback method to detect locale from URL
  const getLocaleFromPath = (): Locale => {
    const pathSegments = pathname.split('/').filter(Boolean);
    const firstSegment = pathSegments[0];

    // Check if first segment is a valid locale
    if (firstSegment && Object.keys(languages).includes(firstSegment)) {
      return firstSegment as Locale;
    }

    // Default to English if no locale in path
    return 'en';
  };

  // Use URL-based detection first, then fallback to hook
  const urlLocale = getLocaleFromPath();
  const currentLocale = urlLocale || hookLocale || 'en';
  const currentLanguage = languages[currentLocale];

  // Debug: Log the values to see what's happening
  console.log('LanguageSwitcher Debug:', {
    pathname,
    hookLocale,
    urlLocale,
    currentLocale,
    currentLanguage: currentLanguage?.nativeName,
    allLanguages: Object.keys(languages)
  });

  const handleLanguageChange = (newLocale: Locale) => {
    // Simple approach: always navigate to the root of the new locale
    // This avoids complex path manipulation and URL stacking issues

    let newPath;
    if (newLocale === 'en') {
      // For English (default locale), go to root
      newPath = '/';
    } else {
      // For other locales, go to locale root
      newPath = `/${newLocale}`;
    }

    // Store the language preference
    document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=31536000`;
    localStorage.setItem('preferred-language', newLocale);

    // Navigate to the new path
    router.push(newPath);
    setIsOpen(false);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          size="sm"
          className="flex items-center gap-2 min-w-[120px]"
        >
          <Globe className="w-4 h-4" />
          <span className="flex items-center gap-1">
            <span>{currentLanguage.flag}</span>
            <span className="hidden sm:inline">{currentLanguage.nativeName}</span>
            <span className="sm:hidden">{currentLocale.toUpperCase()}</span>
          </span>
          <ChevronDown className="w-3 h-3" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-48">
        {Object.entries(languages).map(([locale, language]) => (
          <DropdownMenuItem
            key={locale}
            onClick={() => handleLanguageChange(locale as Locale)}
            className={`flex items-center gap-3 cursor-pointer ${
              locale === currentLocale ? 'bg-accent' : ''
            }`}
          >
            <span className="text-lg">{language.flag}</span>
            <div className="flex flex-col">
              <span className="font-medium">{language.nativeName}</span>
              <span className="text-xs text-muted-foreground">{language.name}</span>
            </div>
            {locale === currentLocale && (
              <div className="ml-auto w-2 h-2 bg-primary rounded-full" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Compact version for mobile/small spaces
export function LanguageSwitcherCompact() {
  const router = useRouter();
  const pathname = usePathname();
  const hookLocale = useLocale() as Locale;
  const [isOpen, setIsOpen] = useState(false);

  // Fallback method to detect locale from URL
  const getLocaleFromPath = (): Locale => {
    const pathSegments = pathname.split('/').filter(Boolean);
    const firstSegment = pathSegments[0];

    if (firstSegment && Object.keys(languages).includes(firstSegment)) {
      return firstSegment as Locale;
    }

    return 'en';
  };

  // Use URL-based detection first, then fallback to hook
  const urlLocale = getLocaleFromPath();
  const currentLocale = urlLocale || hookLocale || 'en';
  const currentLanguage = languages[currentLocale];

  const handleLanguageChange = (newLocale: Locale) => {
    // Simple approach: always navigate to the root of the new locale
    let newPath;
    if (newLocale === 'en') {
      // For English (default locale), go to root
      newPath = '/';
    } else {
      // For other locales, go to locale root
      newPath = `/${newLocale}`;
    }

    document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=31536000`;
    localStorage.setItem('preferred-language', newLocale);

    router.push(newPath);
    setIsOpen(false);
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm"
          className="flex items-center gap-1 px-2"
        >
          <span className="text-lg">{currentLanguage.flag}</span>
          <ChevronDown className="w-3 h-3" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-40">
        {Object.entries(languages).map(([locale, language]) => (
          <DropdownMenuItem
            key={locale}
            onClick={() => handleLanguageChange(locale as Locale)}
            className={`flex items-center gap-2 cursor-pointer ${
              locale === currentLocale ? 'bg-accent' : ''
            }`}
          >
            <span>{language.flag}</span>
            <span className="text-sm">{language.nativeName}</span>
            {locale === currentLocale && (
              <div className="ml-auto w-1.5 h-1.5 bg-primary rounded-full" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

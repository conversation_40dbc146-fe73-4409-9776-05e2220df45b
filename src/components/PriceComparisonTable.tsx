'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  ExternalLink, 
  Globe, 
  Clock, 
  TrendingDown,
  TrendingUp,
  Star,
  Truck,
  CreditCard
} from 'lucide-react';
import { ProductPrice, priceSources } from '@/data/hardwareData';

interface PriceComparisonTableProps {
  prices: ProductPrice[];
  className?: string;
}

export function PriceComparisonTable({ prices, className = '' }: PriceComparisonTableProps) {
  const [sortBy, setSortBy] = useState<'price' | 'availability' | 'source'>('price');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const getSourceDetails = (sourceId: string) => {
    return priceSources.find(s => s.id === sourceId) || {
      id: sourceId,
      name: sourceId,
      website: '#',
      country: 'US',
      currency: 'USD',
      isActive: true
    };
  };

  const sortedPrices = [...prices].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'price':
        comparison = a.price - b.price;
        break;
      case 'availability':
        const availabilityOrder = { 'In Stock': 0, 'Limited Stock': 1, 'Out of Stock': 2 };
        comparison = availabilityOrder[a.availability] - availabilityOrder[b.availability];
        break;
      case 'source':
        const sourceA = getSourceDetails(a.sourceId).name;
        const sourceB = getSourceDetails(b.sourceId).name;
        comparison = sourceA.localeCompare(sourceB);
        break;
    }
    
    return sortOrder === 'asc' ? comparison : -comparison;
  });

  const bestPrice = Math.min(...prices.map(p => p.price));
  const worstPrice = Math.max(...prices.map(p => p.price));

  const handleSort = (column: 'price' | 'availability' | 'source') => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const formatLastUpdated = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just updated';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  if (prices.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <Globe className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Prices Available</h3>
          <p className="text-muted-foreground">
            We're working to find the best prices for this product.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Globe className="w-5 h-5" />
          Price Comparison ({prices.length} sources)
        </CardTitle>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <TrendingDown className="w-4 h-4 text-green-600" />
            Best: ${bestPrice.toLocaleString()}
          </div>
          <div className="flex items-center gap-1">
            <TrendingUp className="w-4 h-4 text-red-600" />
            Highest: ${worstPrice.toLocaleString()}
          </div>
          <div>
            Savings: ${(worstPrice - bestPrice).toLocaleString()}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Sort Controls */}
        <div className="flex gap-2 mb-4">
          <Button
            variant={sortBy === 'price' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleSort('price')}
          >
            Price {sortBy === 'price' && (sortOrder === 'asc' ? '↑' : '↓')}
          </Button>
          <Button
            variant={sortBy === 'availability' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleSort('availability')}
          >
            Availability {sortBy === 'availability' && (sortOrder === 'asc' ? '↑' : '↓')}
          </Button>
          <Button
            variant={sortBy === 'source' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleSort('source')}
          >
            Source {sortBy === 'source' && (sortOrder === 'asc' ? '↑' : '↓')}
          </Button>
        </div>

        {/* Price Comparison Table */}
        <div className="space-y-3">
          {sortedPrices.map((price, index) => {
            const source = getSourceDetails(price.sourceId);
            const isBestPrice = price.price === bestPrice;
            const savings = price.price - bestPrice;
            
            return (
              <div 
                key={price.sourceId} 
                className={`p-4 border rounded-lg transition-all hover:shadow-md ${
                  isBestPrice ? 'border-green-200 bg-green-50 dark:bg-green-950' : ''
                }`}
              >
                <div className="flex items-center justify-between">
                  {/* Source Info */}
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                      <Globe className="w-5 h-5 text-muted-foreground" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-semibold">{source.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {source.country}
                        </Badge>
                        {isBestPrice && (
                          <Badge className="text-xs bg-green-600">
                            <Star className="w-3 h-3 mr-1" />
                            Best Price
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {formatLastUpdated(price.lastUpdated)}
                        </div>
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${
                            price.availability === 'In Stock' 
                              ? 'text-green-600 border-green-200' 
                              : price.availability === 'Limited Stock'
                              ? 'text-orange-600 border-orange-200'
                              : 'text-red-600 border-red-200'
                          }`}
                        >
                          {price.availability}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Price Info */}
                  <div className="text-right">
                    <div className="flex items-center gap-2">
                      <div>
                        <div className="text-2xl font-bold">
                          ${price.price.toLocaleString()}
                        </div>
                        {price.originalPrice && price.originalPrice > price.price && (
                          <div className="text-sm text-muted-foreground line-through">
                            ${price.originalPrice.toLocaleString()}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {savings > 0 && (
                      <div className="text-sm text-red-600 mt-1">
                        +${savings.toLocaleString()} vs best
                      </div>
                    )}
                    
                    {/* Additional costs */}
                    <div className="flex items-center gap-4 text-xs text-muted-foreground mt-2">
                      {price.shipping && (
                        <div className="flex items-center gap-1">
                          <Truck className="w-3 h-3" />
                          +${price.shipping}
                        </div>
                      )}
                      {price.tax && (
                        <div className="flex items-center gap-1">
                          <CreditCard className="w-3 h-3" />
                          +${price.tax}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Action Button */}
                  <div className="ml-4">
                    <Button
                      variant={isBestPrice ? 'default' : 'outline'}
                      onClick={() => window.open(`https://${source.website}`, '_blank')}
                      className={isBestPrice ? 'bg-green-600 hover:bg-green-700' : ''}
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      {isBestPrice ? 'Buy Now' : 'View Deal'}
                    </Button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Summary */}
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="font-semibold text-green-600">${bestPrice.toLocaleString()}</div>
              <div className="text-muted-foreground">Best Price</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">${(worstPrice - bestPrice).toLocaleString()}</div>
              <div className="text-muted-foreground">Max Savings</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">{prices.length}</div>
              <div className="text-muted-foreground">Sources</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">
                {prices.filter(p => p.availability === 'In Stock').length}
              </div>
              <div className="text-muted-foreground">In Stock</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

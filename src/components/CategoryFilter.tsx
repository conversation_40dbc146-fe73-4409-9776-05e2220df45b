import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Cpu, 
  Monitor, 
  HardDrive, 
  Database, 
  CircuitBoard, 
  Zap,
  Grid3X3,
  LucideIcon
} from 'lucide-react';
import { hardwareCategories, getProductsByCategory, HardwareCategory } from '@/data/hardwareData';

const iconMap: Record<string, LucideIcon> = {
  Cpu,
  Monitor,
  HardDrive,
  Database,
  CircuitBoard,
  Zap,
  Grid3X3
};

interface CategoryFilterProps {
  selectedCategory: string;
  onCategoryChange: (categoryId: string) => void;
}

export function CategoryFilter({ selectedCategory, onCategoryChange }: CategoryFilterProps) {
  const allCategory: HardwareCategory = {
    id: 'all',
    name: 'All Categories',
    icon: 'Grid3X3',
    description: 'View all hardware'
  };
  
  const categories = [allCategory, ...hardwareCategories];
  
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Categories</h2>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-7 gap-3">
        {categories.map((category) => {
          const IconComponent = iconMap[category.icon];
          const productCount = category.id === 'all' 
            ? hardwareCategories.reduce((total, cat) => total + getProductsByCategory(cat.id).length, 0)
            : getProductsByCategory(category.id).length;
          
          return (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              className={`h-auto p-4 flex flex-col items-center gap-2 transition-all duration-200 hover:scale-105 ${
                selectedCategory === category.id 
                  ? 'bg-blue-600 hover:bg-blue-700 shadow-lg' 
                  : 'hover:bg-blue-50 hover:border-blue-300'
              }`}
              onClick={() => onCategoryChange(category.id)}
            >
              <IconComponent className="w-6 h-6" />
              <div className="text-center">
                <div className="text-sm font-medium leading-tight">
                  {category.name}
                </div>
                <Badge 
                  variant="secondary" 
                  className={`mt-1 text-xs ${
                    selectedCategory === category.id 
                      ? 'bg-blue-100 text-blue-800' 
                      : 'bg-gray-100 text-gray-600'
                  }`}
                >
                  {productCount}
                </Badge>
              </div>
            </Button>
          );
        })}
      </div>
    </div>
  );
}


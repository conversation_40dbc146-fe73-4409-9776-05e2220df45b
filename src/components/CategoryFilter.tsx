import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Cpu,
  Monitor,
  HardDrive,
  Database,
  CircuitBoard,
  Zap,
  Grid3X3,
  LucideIcon,
  AlertCircle
} from 'lucide-react';
import { hardwareCategories, getProductsByCategory, HardwareCategory } from '@/data/hardwareData';
import { useMemo, useCallback } from 'react';

// Strict typing for icon mapping with fallback
type IconName = 'Cpu' | 'Monitor' | 'HardDrive' | 'Database' | 'CircuitBoard' | 'Zap' | 'Grid3X3';

const iconMap: Record<IconName, LucideIcon> = {
  Cpu,
  Monitor,
  HardDrive,
  Database,
  CircuitBoard,
  Zap,
  Grid3X3
};

// Fallback icon for missing or invalid icons
const getIconComponent = (iconName: string): LucideIcon => {
  return iconMap[iconName as IconName] || AlertCircle;
};

interface CategoryFilterProps {
  selectedCategory: string;
  onCategoryChange: (categoryId: string) => void;
  isLoading?: boolean;
  error?: string | null;
}

// Style constants for better maintainability
const styles = {
  selectedButton: 'bg-blue-600 hover:bg-blue-700 shadow-lg',
  unselectedButton: 'hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-gray-800 dark:hover:border-blue-400',
  selectedBadge: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  unselectedBadge: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-300',
  baseButton: 'h-auto p-3 sm:p-4 flex flex-col items-center gap-2 transition-all duration-200 hover:scale-105 focus:scale-105 min-h-[80px] sm:min-h-[100px]',
  grid: 'grid grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-7 gap-2 sm:gap-3',
  icon: 'w-5 h-5 sm:w-6 sm:h-6 flex-shrink-0',
  categoryName: 'text-xs sm:text-sm font-medium leading-tight text-center line-clamp-2',
  badge: 'mt-1 text-xs px-1.5 py-0.5'
} as const;

export function CategoryFilter({
  selectedCategory,
  onCategoryChange,
  isLoading = false,
  error = null
}: CategoryFilterProps) {
  // Memoize the all category to prevent recreation on every render
  const allCategory: HardwareCategory = useMemo(() => ({
    id: 'all',
    name: 'All Categories',
    icon: 'Grid3X3',
    description: 'View all hardware'
  }), []);

  // Memoize categories array
  const categories = useMemo(() => [allCategory, ...hardwareCategories], [allCategory]);

  // Memoize product counts to avoid expensive calculations on every render
  const productCounts = useMemo(() => {
    const counts: Record<string, number> = {};

    try {
      // Calculate total count for 'all' category
      counts.all = hardwareCategories.reduce((total, cat) =>
        total + getProductsByCategory(cat.id).length, 0
      );

      // Calculate individual category counts
      hardwareCategories.forEach(category => {
        counts[category.id] = getProductsByCategory(category.id).length;
      });
    } catch (err) {
      console.error('Error calculating product counts:', err);
      // Return empty counts on error
      return {};
    }

    return counts;
  }, []);

  // Memoize click handler to prevent unnecessary re-renders
  const handleCategoryClick = useCallback((categoryId: string) => {
    onCategoryChange(categoryId);
  }, [onCategoryChange]);

  // Loading skeleton component
  const CategorySkeleton = () => (
    <div className={styles.baseButton.replace('hover:scale-105', '')} aria-hidden="true">
      <div className="w-6 h-6 skeleton rounded"></div>
      <div className="text-center space-y-1">
        <div className="h-4 w-16 skeleton rounded"></div>
        <div className="h-5 w-8 skeleton rounded"></div>
      </div>
    </div>
  );

  // Error state component
  const ErrorState = () => (
    <div className="col-span-full text-center py-8">
      <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-foreground mb-2">
        Unable to load categories
      </h3>
      <p className="text-muted-foreground mb-4">
        {error || 'An unexpected error occurred while loading category data.'}
      </p>
      <Button
        variant="outline"
        onClick={() => window.location.reload()}
        className="text-sm"
      >
        Try Again
      </Button>
    </div>
  );
  
  // Show error state if there's an error
  if (error) {
    return (
      <div className="space-y-4">
        <h2 className="text-xl font-semibold" id="categories-heading">
          Categories
        </h2>
        <div className={styles.grid}>
          <ErrorState />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold" id="categories-heading">
        Categories
      </h2>

      <div
        className={styles.grid}
        role="tablist"
        aria-labelledby="categories-heading"
        aria-describedby="categories-description"
      >
        <div id="categories-description" className="sr-only">
          Select a category to filter hardware products
        </div>

        {(isLoading || isCalculating) ? (
          // Show loading skeletons
          Array.from({ length: 7 }, (_, index) => (
            <CategorySkeleton key={`skeleton-${index}`} />
          ))
        ) : (
          categories.map((category, index) => {
            const IconComponent = getIconComponent(category.icon);
            const productCount = productCounts[category.id] || 0;
            const isSelected = selectedCategory === category.id;

          return (
            <Button
              key={category.id}
              variant={isSelected ? "default" : "outline"}
              className={`${styles.baseButton} ${
                isSelected ? styles.selectedButton : styles.unselectedButton
              }`}
              onClick={() => handleCategoryClick(category.id)}
              role="tab"
              aria-selected={isSelected}
              aria-controls={`products-${category.id}`}
              aria-describedby={`category-desc-${category.id}`}
              tabIndex={isSelected ? 0 : -1}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  handleCategoryClick(category.id);
                } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                  e.preventDefault();
                  const nextIndex = (index + 1) % categories.length;
                  const nextButton = e.currentTarget.parentElement?.children[nextIndex] as HTMLButtonElement;
                  nextButton?.focus();
                } else if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                  e.preventDefault();
                  const prevIndex = index === 0 ? categories.length - 1 : index - 1;
                  const prevButton = e.currentTarget.parentElement?.children[prevIndex] as HTMLButtonElement;
                  prevButton?.focus();
                }
              }}
            >
              <IconComponent
                className={styles.icon}
                aria-hidden="true"
              />
              <div className="text-center flex-1 min-w-0">
                <div className={styles.categoryName}>
                  {category.name}
                </div>
                <Badge
                  variant="secondary"
                  className={`${styles.badge} ${
                    isSelected ? styles.selectedBadge : styles.unselectedBadge
                  }`}
                  aria-label={`${productCount} products in ${category.name}`}
                >
                  {productCount}
                </Badge>
              </div>
              <div id={`category-desc-${category.id}`} className="sr-only">
                {category.description}. {productCount} products available.
              </div>
            </Button>
          );
        })
        )}
      </div>
    </div>
  );
}


import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Star, TrendingDown, TrendingUp, ShoppingCart, ExternalLink, Globe } from 'lucide-react';
import { HardwareProduct, calculatePriceChange, priceSources } from '@/data/hardwareData';
import { useRouter } from 'next/navigation';

interface ProductCardProps {
  product: HardwareProduct;
  showThumbnail?: boolean; // For future use when product images are available
  showMultiplePrices?: boolean; // Show prices from multiple sources
  maxPriceSources?: number; // Maximum number of price sources to display
}

export function ProductCard({
  product,
  showThumbnail = false,
  showMultiplePrices = true,
  maxPriceSources = 3
}: ProductCardProps) {
  const router = useRouter();
  const priceChange = calculatePriceChange(product);

  // Get the best price (lowest) from all sources
  const bestPrice = product.prices.length > 0
    ? Math.min(...product.prices.map(p => p.price))
    : product.currentPrice;

  // Get sorted prices (best first)
  const sortedPrices = [...product.prices]
    .sort((a, b) => a.price - b.price)
    .slice(0, maxPriceSources);

  // Helper function to get source name
  const getSourceName = (sourceId: string) => {
    const source = priceSources.find(s => s.id === sourceId);
    return source ? source.name : sourceId;
  };

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-0 shadow-md">
      {/* Thumbnail section - hidden by default, ready for future use */}
      {showThumbnail && (
        <CardHeader className="p-0">
          <div className="relative overflow-hidden rounded-t-lg bg-gradient-to-br from-gray-50 to-gray-100 h-48">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-32 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                {product.brand}
              </div>
            </div>
            <Badge
              className={`absolute top-3 right-3 ${
                product.availability === 'In Stock'
                  ? 'bg-green-500 hover:bg-green-600'
                  : product.availability === 'Limited Stock'
                  ? 'bg-orange-500 hover:bg-orange-600'
                  : 'bg-red-500 hover:bg-red-600'
              }`}
            >
              {product.availability}
            </Badge>
          </div>
        </CardHeader>
      )}

      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="relative">
            {/* Availability badge - moved from thumbnail to content area */}
            <Badge
              className={`absolute top-0 right-0 ${
                product.availability === 'In Stock'
                  ? 'bg-green-500 hover:bg-green-600'
                  : product.availability === 'Limited Stock'
                  ? 'bg-orange-500 hover:bg-orange-600'
                  : 'bg-red-500 hover:bg-red-600'
              }`}
            >
              {product.availability}
            </Badge>

            <h3 className="font-semibold text-lg leading-tight group-hover:text-blue-600 transition-colors pr-20">
              {product.name}
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              {product.specs}
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              <span className="text-sm font-medium ml-1">{product.rating}</span>
            </div>
            <span className="text-sm text-muted-foreground">
              ({product.reviews.toLocaleString()} reviews)
            </span>
          </div>
          
          <div className="space-y-3">
            {/* Primary price display */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-green-600">
                  ${bestPrice.toLocaleString()}
                </span>
                {priceChange.isDecrease && (
                  <span className="text-sm text-muted-foreground line-through">
                    ${product.originalPrice.toLocaleString()}
                  </span>
                )}
                {product.prices.length > 1 && (
                  <Badge variant="secondary" className="text-xs">
                    Best Price
                  </Badge>
                )}
              </div>

              {priceChange.amount !== 0 && (
                <div className={`flex items-center gap-1 text-sm font-medium ${
                  priceChange.isDecrease ? 'text-green-600' : 'text-red-600'
                }`}>
                  {priceChange.isDecrease ? (
                    <TrendingDown className="w-4 h-4" />
                  ) : (
                    <TrendingUp className="w-4 h-4" />
                  )}
                  {Math.abs(parseFloat(priceChange.percentage))}%
                </div>
              )}
            </div>

            {/* Multiple price sources */}
            {showMultiplePrices && sortedPrices.length > 0 && (
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">
                  Available at:
                </div>
                <div className="space-y-1">
                  {sortedPrices.map((price, index) => (
                    <div key={price.sourceId} className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-2">
                        <Globe className="w-3 h-3 text-muted-foreground" />
                        <span className="font-medium">{getSourceName(price.sourceId)}</span>
                        <Badge
                          variant="outline"
                          className={`text-xs ${
                            price.availability === 'In Stock'
                              ? 'text-green-600 border-green-200'
                              : price.availability === 'Limited Stock'
                              ? 'text-orange-600 border-orange-200'
                              : 'text-red-600 border-red-200'
                          }`}
                        >
                          {price.availability}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        {index === 0 && sortedPrices.length > 1 && (
                          <Badge variant="default" className="text-xs bg-green-600">
                            Best
                          </Badge>
                        )}
                        <span className="font-semibold">${price.price.toLocaleString()}</span>
                      </div>
                    </div>
                  ))}
                  {product.prices.length > maxPriceSources && (
                    <div className="text-xs text-muted-foreground text-center pt-1">
                      +{product.prices.length - maxPriceSources} more sources
                    </div>
                  )}
                </div>
              </div>
            )}

            {priceChange.isDecrease && (
              <div className="text-sm text-green-600 font-medium">
                Save ${Math.abs(priceChange.amount).toLocaleString()}
              </div>
            )}
          </div>
        </div>
      </CardContent>

      <CardFooter className="p-4 pt-0">
        <div className="flex gap-2 w-full">
          <Button
            className="flex-1 group-hover:bg-blue-600 transition-colors"
            onClick={() => router.push(`/product/${product.id}`)}
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            View Details
          </Button>
          {sortedPrices.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              className="px-3"
              onClick={() => router.push(`/product/${product.id}`)}
            >
              <ShoppingCart className="w-4 h-4" />
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}


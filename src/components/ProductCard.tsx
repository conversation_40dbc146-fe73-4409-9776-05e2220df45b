import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Star, TrendingDown, TrendingUp, ShoppingCart } from 'lucide-react';
import { HardwareProduct, calculatePriceChange } from '@/data/hardwareData';

interface ProductCardProps {
  product: HardwareProduct;
}

export function ProductCard({ product }: ProductCardProps) {
  const priceChange = calculatePriceChange(product);
  
  return (
    <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-0 shadow-md">
      <CardHeader className="p-0">
        <div className="relative overflow-hidden rounded-t-lg bg-gradient-to-br from-gray-50 to-gray-100 h-48">
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-32 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-lg">
              {product.brand}
            </div>
          </div>
          <Badge 
            className={`absolute top-3 right-3 ${
              product.availability === 'In Stock' 
                ? 'bg-green-500 hover:bg-green-600' 
                : product.availability === 'Limited Stock'
                ? 'bg-orange-500 hover:bg-orange-600'
                : 'bg-red-500 hover:bg-red-600'
            }`}
          >
            {product.availability}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="p-4">
        <div className="space-y-3">
          <div>
            <h3 className="font-semibold text-lg leading-tight group-hover:text-blue-600 transition-colors">
              {product.name}
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              {product.specs}
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center">
              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
              <span className="text-sm font-medium ml-1">{product.rating}</span>
            </div>
            <span className="text-sm text-muted-foreground">
              ({product.reviews.toLocaleString()} reviews)
            </span>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold text-green-600">
                  ${product.currentPrice.toLocaleString()}
                </span>
                {priceChange.isDecrease && (
                  <span className="text-sm text-muted-foreground line-through">
                    ${product.originalPrice.toLocaleString()}
                  </span>
                )}
              </div>
              
              {priceChange.amount !== 0 && (
                <div className={`flex items-center gap-1 text-sm font-medium ${
                  priceChange.isDecrease ? 'text-green-600' : 'text-red-600'
                }`}>
                  {priceChange.isDecrease ? (
                    <TrendingDown className="w-4 h-4" />
                  ) : (
                    <TrendingUp className="w-4 h-4" />
                  )}
                  {Math.abs(parseFloat(priceChange.percentage))}%
                </div>
              )}
            </div>
            
            {priceChange.isDecrease && (
              <div className="text-sm text-green-600 font-medium">
                Save ${Math.abs(priceChange.amount).toLocaleString()}
              </div>
            )}
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="p-4 pt-0">
        <Button className="w-full group-hover:bg-blue-600 transition-colors">
          <ShoppingCart className="w-4 h-4 mr-2" />
          View Details
        </Button>
      </CardFooter>
    </Card>
  );
}


'use client';

import { useMemo } from 'react';
import { PriceHistory } from '@/data/hardwareData';

interface PriceHistoryChartProps {
  priceHistory: PriceHistory[];
  timeframe: string;
  className?: string;
}

export function PriceHistoryChart({ priceHistory, timeframe, className = '' }: PriceHistoryChartProps) {
  const chartData = useMemo(() => {
    if (!priceHistory || priceHistory.length === 0) return [];
    
    // Filter data based on timeframe
    const now = new Date();
    const cutoffDate = new Date();
    
    switch (timeframe) {
      case '1M':
        cutoffDate.setMonth(now.getMonth() - 1);
        break;
      case '3M':
        cutoffDate.setMonth(now.getMonth() - 3);
        break;
      case '6M':
        cutoffDate.setMonth(now.getMonth() - 6);
        break;
      case '1Y':
        cutoffDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        cutoffDate.setMonth(now.getMonth() - 3);
    }
    
    return priceHistory
      .filter(item => new Date(item.date) >= cutoffDate)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }, [priceHistory, timeframe]);

  const { minPrice, maxPrice, priceRange } = useMemo(() => {
    if (chartData.length === 0) return { minPrice: 0, maxPrice: 0, priceRange: 0 };
    
    const prices = chartData.map(item => item.price);
    const min = Math.min(...prices);
    const max = Math.max(...prices);
    const range = max - min;
    
    return {
      minPrice: min,
      maxPrice: max,
      priceRange: range
    };
  }, [chartData]);

  if (chartData.length === 0) {
    return (
      <div className={`h-64 bg-muted rounded-lg flex items-center justify-center ${className}`}>
        <p className="text-muted-foreground">No price history available for this timeframe</p>
      </div>
    );
  }

  // Calculate SVG path for the price line
  const svgWidth = 800;
  const svgHeight = 200;
  const padding = 40;
  const chartWidth = svgWidth - (padding * 2);
  const chartHeight = svgHeight - (padding * 2);

  const pathData = chartData.map((item, index) => {
    const x = padding + (index / (chartData.length - 1)) * chartWidth;
    const y = padding + ((maxPrice - item.price) / priceRange) * chartHeight;
    return `${index === 0 ? 'M' : 'L'} ${x} ${y}`;
  }).join(' ');

  const currentPrice = chartData[chartData.length - 1]?.price || 0;
  const previousPrice = chartData[0]?.price || 0;
  const priceChange = currentPrice - previousPrice;
  const priceChangePercent = previousPrice > 0 ? ((priceChange / previousPrice) * 100).toFixed(1) : '0.0';
  const isDecrease = priceChange < 0;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Price Change Summary */}
      <div className="flex items-center justify-between">
        <div>
          <div className="text-2xl font-bold">
            ${currentPrice.toLocaleString()}
          </div>
          <div className={`flex items-center gap-1 text-sm ${
            isDecrease ? 'text-green-600' : 'text-red-600'
          }`}>
            <span>
              {isDecrease ? '-' : '+'}${Math.abs(priceChange).toLocaleString()}
            </span>
            <span>
              ({isDecrease ? '-' : '+'}${Math.abs(parseFloat(priceChangePercent))}%)
            </span>
            <span className="text-muted-foreground">
              vs {timeframe} ago
            </span>
          </div>
        </div>
        <div className="text-right text-sm text-muted-foreground">
          <div>High: ${maxPrice.toLocaleString()}</div>
          <div>Low: ${minPrice.toLocaleString()}</div>
        </div>
      </div>

      {/* Chart */}
      <div className="relative bg-card border rounded-lg p-4">
        <svg 
          width="100%" 
          height={svgHeight} 
          viewBox={`0 0 ${svgWidth} ${svgHeight}`}
          className="overflow-visible"
        >
          {/* Grid lines */}
          <defs>
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="0.5" opacity="0.1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          {/* Price line */}
          <path
            d={pathData}
            fill="none"
            stroke={isDecrease ? '#16a34a' : '#dc2626'}
            strokeWidth="2"
            className="drop-shadow-sm"
          />
          
          {/* Data points */}
          {chartData.map((item, index) => {
            const x = padding + (index / (chartData.length - 1)) * chartWidth;
            const y = padding + ((maxPrice - item.price) / priceRange) * chartHeight;
            
            return (
              <g key={index}>
                <circle
                  cx={x}
                  cy={y}
                  r="4"
                  fill={isDecrease ? '#16a34a' : '#dc2626'}
                  className="drop-shadow-sm"
                />
                {/* Tooltip on hover */}
                <circle
                  cx={x}
                  cy={y}
                  r="12"
                  fill="transparent"
                  className="cursor-pointer hover:fill-black hover:fill-opacity-5"
                >
                  <title>
                    {new Date(item.date).toLocaleDateString()}: ${item.price.toLocaleString()}
                  </title>
                </circle>
              </g>
            );
          })}
          
          {/* Y-axis labels */}
          <text x="10" y={padding} className="text-xs fill-muted-foreground" dominantBaseline="middle">
            ${maxPrice.toLocaleString()}
          </text>
          <text x="10" y={svgHeight - padding} className="text-xs fill-muted-foreground" dominantBaseline="middle">
            ${minPrice.toLocaleString()}
          </text>
          
          {/* X-axis labels */}
          <text x={padding} y={svgHeight - 10} className="text-xs fill-muted-foreground" textAnchor="start">
            {new Date(chartData[0].date).toLocaleDateString()}
          </text>
          <text x={svgWidth - padding} y={svgHeight - 10} className="text-xs fill-muted-foreground" textAnchor="end">
            {new Date(chartData[chartData.length - 1].date).toLocaleDateString()}
          </text>
        </svg>
      </div>

      {/* Data Points Summary */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div className="text-center p-2 bg-muted rounded">
          <div className="font-semibold">{chartData.length}</div>
          <div className="text-muted-foreground">Data Points</div>
        </div>
        <div className="text-center p-2 bg-muted rounded">
          <div className="font-semibold">${(priceRange).toLocaleString()}</div>
          <div className="text-muted-foreground">Price Range</div>
        </div>
        <div className="text-center p-2 bg-muted rounded">
          <div className="font-semibold">
            ${((chartData.reduce((sum, item) => sum + item.price, 0)) / chartData.length).toFixed(0)}
          </div>
          <div className="text-muted-foreground">Average</div>
        </div>
        <div className="text-center p-2 bg-muted rounded">
          <div className={`font-semibold ${isDecrease ? 'text-green-600' : 'text-red-600'}`}>
            {isDecrease ? '↓' : '↑'} {Math.abs(parseFloat(priceChangePercent))}%
          </div>
          <div className="text-muted-foreground">Change</div>
        </div>
      </div>
    </div>
  );
}

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CategoryFilter } from '../CategoryFilter';

// Mock the hardware data
jest.mock('@/data/hardwareData', () => ({
  hardwareCategories: [
    {
      id: 'cpu',
      name: 'Processors (CPU)',
      icon: 'Cpu',
      description: 'Central Processing Units'
    },
    {
      id: 'gpu',
      name: 'Graphics Cards (GPU)',
      icon: 'Monitor',
      description: 'Graphics Processing Units'
    }
  ],
  getProductsByCategory: jest.fn((categoryId: string) => {
    const mockProducts = {
      cpu: [{ id: '1' }, { id: '2' }],
      gpu: [{ id: '3' }, { id: '4' }, { id: '5' }]
    };
    return mockProducts[categoryId as keyof typeof mockProducts] || [];
  })
}));

describe('CategoryFilter', () => {
  const mockOnCategoryChange = jest.fn();

  beforeEach(() => {
    mockOnCategoryChange.mockClear();
  });

  it('renders all categories including "All Categories"', () => {
    render(
      <CategoryFilter
        selectedCategory="all"
        onCategoryChange={mockOnCategoryChange}
      />
    );

    expect(screen.getByText('All Categories')).toBeInTheDocument();
    expect(screen.getByText('Processors (CPU)')).toBeInTheDocument();
    expect(screen.getByText('Graphics Cards (GPU)')).toBeInTheDocument();
  });

  it('displays correct product counts', async () => {
    render(
      <CategoryFilter
        selectedCategory="all"
        onCategoryChange={mockOnCategoryChange}
      />
    );

    // Wait for product counts to be calculated
    await waitFor(() => {
      expect(screen.getByLabelText('5 products in All Categories')).toBeInTheDocument();
      expect(screen.getByLabelText('2 products in Processors (CPU)')).toBeInTheDocument();
      expect(screen.getByLabelText('3 products in Graphics Cards (GPU)')).toBeInTheDocument();
    });
  });

  it('calls onCategoryChange when a category is clicked', () => {
    render(
      <CategoryFilter
        selectedCategory="all"
        onCategoryChange={mockOnCategoryChange}
      />
    );

    fireEvent.click(screen.getByText('Processors (CPU)'));
    expect(mockOnCategoryChange).toHaveBeenCalledWith('cpu');
  });

  it('supports keyboard navigation', () => {
    render(
      <CategoryFilter
        selectedCategory="all"
        onCategoryChange={mockOnCategoryChange}
      />
    );

    const allCategoriesButton = screen.getByRole('tab', { name: /All Categories/ });
    allCategoriesButton.focus();

    // Test Enter key
    fireEvent.keyDown(allCategoriesButton, { key: 'Enter' });
    expect(mockOnCategoryChange).toHaveBeenCalledWith('all');

    // Test Space key
    fireEvent.keyDown(allCategoriesButton, { key: ' ' });
    expect(mockOnCategoryChange).toHaveBeenCalledWith('all');
  });

  it('shows loading skeletons when isLoading is true', () => {
    render(
      <CategoryFilter
        selectedCategory="all"
        onCategoryChange={mockOnCategoryChange}
        isLoading={true}
      />
    );

    // Should show skeleton elements
    const skeletons = screen.getAllByRole('generic', { hidden: true });
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('shows error state when error prop is provided', () => {
    const errorMessage = 'Failed to load categories';
    render(
      <CategoryFilter
        selectedCategory="all"
        onCategoryChange={mockOnCategoryChange}
        error={errorMessage}
      />
    );

    expect(screen.getByText('Unable to load categories')).toBeInTheDocument();
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  it('applies correct ARIA attributes for accessibility', () => {
    render(
      <CategoryFilter
        selectedCategory="cpu"
        onCategoryChange={mockOnCategoryChange}
      />
    );

    const tablist = screen.getByRole('tablist');
    expect(tablist).toHaveAttribute('aria-labelledby', 'categories-heading');
    expect(tablist).toHaveAttribute('aria-describedby', 'categories-description');

    const selectedTab = screen.getByRole('tab', { selected: true });
    expect(selectedTab).toHaveAttribute('aria-selected', 'true');
    expect(selectedTab).toHaveAttribute('tabIndex', '0');
  });

  it('handles missing icons gracefully', () => {
    // This test ensures the fallback icon (AlertCircle) is used for invalid icon names
    render(
      <CategoryFilter
        selectedCategory="all"
        onCategoryChange={mockOnCategoryChange}
      />
    );

    // The component should render without crashing even with invalid icon names
    expect(screen.getByText('Categories')).toBeInTheDocument();
  });
});

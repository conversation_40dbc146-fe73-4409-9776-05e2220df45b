// Types for hardware data

// New types for multi-site, multi-country support
export interface PriceSource {
  id: string;
  name: string; // "Amazon", "Newegg", "Best Buy", etc.
  logo?: string;
  website: string;
  country: string;
  currency: string;
  isActive: boolean;
}

export interface ProductPrice {
  sourceId: string;
  price: number;
  originalPrice?: number;
  currency: string;
  availability: 'In Stock' | 'Limited Stock' | 'Out of Stock';
  lastUpdated: string;
  productUrl: string;
  shipping?: number;
  tax?: number;
}

export interface PriceHistory {
  sourceId: string;
  date: string;
  price: number;
  currency: string;
}

export interface Country {
  code: string; // "US", "UK", "DE", "JP"
  name: string;
  currency: string;
  flag: string;
  locale: string;
}

// Updated product interface with multi-site support
export interface HardwareProduct {
  id: string;
  category: string;
  name: string;
  brand: string;
  model: string;
  specs: string;
  // Legacy fields for backward compatibility
  currentPrice: number;
  originalPrice: number;
  availability: 'In Stock' | 'Limited Stock' | 'Out of Stock';
  // New multi-site fields
  prices: ProductPrice[];
  priceHistory: PriceHistory[];
  rating: number;
  reviews: number;
  image?: string; // Optional for future use
  globalId: string; // For matching across regions
}

export interface HardwareCategory {
  id: string;
  name: string;
  icon: string;
  description: string;
}

// Sample price sources for different countries
export const priceSources: PriceSource[] = [
  {
    id: 'amazon-us',
    name: 'Amazon US',
    website: 'amazon.com',
    country: 'US',
    currency: 'USD',
    isActive: true
  },
  {
    id: 'newegg-us',
    name: 'Newegg US',
    website: 'newegg.com',
    country: 'US',
    currency: 'USD',
    isActive: true
  },
  {
    id: 'bestbuy-us',
    name: 'Best Buy US',
    website: 'bestbuy.com',
    country: 'US',
    currency: 'USD',
    isActive: true
  },
  {
    id: 'amazon-uk',
    name: 'Amazon UK',
    website: 'amazon.co.uk',
    country: 'UK',
    currency: 'GBP',
    isActive: true
  },
  {
    id: 'amazon-de',
    name: 'Amazon DE',
    website: 'amazon.de',
    country: 'DE',
    currency: 'EUR',
    isActive: true
  }
];

// Supported countries
export const countries: Country[] = [
  {
    code: 'US',
    name: 'United States',
    currency: 'USD',
    flag: '🇺🇸',
    locale: 'en-US'
  },
  {
    code: 'UK',
    name: 'United Kingdom',
    currency: 'GBP',
    flag: '🇬🇧',
    locale: 'en-GB'
  },
  {
    code: 'DE',
    name: 'Germany',
    currency: 'EUR',
    flag: '🇩🇪',
    locale: 'de-DE'
  },
  {
    code: 'CA',
    name: 'Canada',
    currency: 'CAD',
    flag: '🇨🇦',
    locale: 'en-CA'
  },
  {
    code: 'JP',
    name: 'Japan',
    currency: 'JPY',
    flag: '🇯🇵',
    locale: 'ja-JP'
  }
];

// Hardware categories data
export const hardwareCategories: HardwareCategory[] = [
  {
    id: 'cpu',
    name: 'Processors (CPU)',
    icon: 'Cpu',
    description: 'Central Processing Units'
  },
  {
    id: 'gpu',
    name: 'Graphics Cards (GPU)',
    icon: 'Monitor',
    description: 'Graphics Processing Units'
  },
  {
    id: 'ram',
    name: 'Memory (RAM)',
    icon: 'HardDrive',
    description: 'Random Access Memory'
  },
  {
    id: 'storage',
    name: 'Storage',
    icon: 'Database',
    description: 'SSDs and HDDs'
  },
  {
    id: 'motherboard',
    name: 'Motherboards',
    icon: 'CircuitBoard',
    description: 'Main circuit boards'
  },
  {
    id: 'psu',
    name: 'Power Supplies',
    icon: 'Zap',
    description: 'Power Supply Units'
  }
];

// Helper function to create sample multi-site price data
const createSamplePrices = (basePrice: number, originalPrice: number, availability: 'In Stock' | 'Limited Stock' | 'Out of Stock'): ProductPrice[] => [
  {
    sourceId: 'amazon-us',
    price: basePrice,
    originalPrice: originalPrice,
    currency: 'USD',
    availability,
    lastUpdated: '2024-05-01T10:00:00Z',
    productUrl: '#'
  },
  {
    sourceId: 'newegg-us',
    price: basePrice + 20,
    originalPrice: originalPrice + 20,
    currency: 'USD',
    availability,
    lastUpdated: '2024-05-01T10:00:00Z',
    productUrl: '#'
  },
  {
    sourceId: 'bestbuy-us',
    price: basePrice + 30,
    originalPrice: originalPrice + 30,
    currency: 'USD',
    availability: availability === 'In Stock' ? 'Limited Stock' : availability,
    lastUpdated: '2024-05-01T10:00:00Z',
    productUrl: '#'
  }
];

// Helper function to create sample price history
const createSamplePriceHistory = (prices: number[], sourceId: string = 'amazon-us'): PriceHistory[] =>
  prices.map((price, index) => ({
    sourceId,
    date: `2024-0${index + 1}-01`,
    price,
    currency: 'USD'
  }));

// Hardware products data
export const hardwareProducts: HardwareProduct[] = [
  // CPUs
  {
    id: 'cpu-1',
    category: 'cpu',
    name: 'AMD Ryzen 9 7950X',
    brand: 'AMD',
    model: '7950X',
    specs: '16 Cores, 32 Threads, 4.5GHz Base',
    currentPrice: 699,
    originalPrice: 799,
    availability: 'In Stock',
    prices: createSamplePrices(699, 799, 'In Stock'),
    priceHistory: createSamplePriceHistory([799, 779, 749, 729, 699]),
    rating: 4.8,
    reviews: 1247,
    image: '/api/placeholder/300/200',
    globalId: 'amd-ryzen-9-7950x'
  },
  {
    id: 'cpu-2',
    category: 'cpu',
    name: 'Intel Core i9-14900K',
    brand: 'Intel',
    model: 'i9-14900K',
    specs: '24 Cores, 32 Threads, 3.2GHz Base',
    currentPrice: 589,
    originalPrice: 649,
    availability: 'In Stock',
    prices: createSamplePrices(589, 649, 'In Stock'),
    priceHistory: createSamplePriceHistory([649, 629, 609, 599, 589]),
    rating: 4.7,
    reviews: 892,
    image: '/api/placeholder/300/200',
    globalId: 'intel-core-i9-13900k'
  },
  // GPUs
  {
    id: 'gpu-1',
    category: 'gpu',
    name: 'NVIDIA RTX 4090',
    brand: 'NVIDIA',
    model: 'RTX 4090',
    specs: '24GB GDDR6X, 2520MHz Boost',
    currentPrice: 1599,
    originalPrice: 1699,
    availability: 'Limited Stock',
    prices: createSamplePrices(1599, 1699, 'Limited Stock'),
    priceHistory: createSamplePriceHistory([1699, 1679, 1649, 1629, 1599]),
    rating: 4.9,
    reviews: 2156,
    image: '/api/placeholder/300/200',
    globalId: 'nvidia-rtx-4090'
  },
  {
    id: 'gpu-2',
    category: 'gpu',
    name: 'AMD RX 7900 XTX',
    brand: 'AMD',
    model: 'RX 7900 XTX',
    specs: '24GB GDDR6, 2500MHz Boost',
    currentPrice: 999,
    originalPrice: 1099,
    availability: 'In Stock',
    prices: createSamplePrices(999, 1099, 'In Stock'),
    priceHistory: createSamplePriceHistory([1099, 1079, 1049, 1029, 999]),
    rating: 4.6,
    reviews: 1543,
    image: '/api/placeholder/300/200',
    globalId: 'amd-rx-7900-xtx'
  },
  // RAM
  {
    id: 'ram-1',
    category: 'ram',
    name: 'Corsair Vengeance LPX 32GB',
    brand: 'Corsair',
    model: 'CMK32GX4M2E3200C16',
    specs: '32GB (2x16GB) DDR4-3200 CL16',
    currentPrice: 89,
    originalPrice: 119,
    availability: 'In Stock',
    prices: createSamplePrices(89, 119, 'In Stock'),
    priceHistory: createSamplePriceHistory([119, 109, 99, 94, 89]),
    rating: 4.7,
    reviews: 3421,
    image: '/api/placeholder/300/200',
    globalId: 'corsair-vengeance-lpx-32gb'
  },
  {
    id: 'ram-2',
    category: 'ram',
    name: 'G.Skill Trident Z5 32GB',
    brand: 'G.Skill',
    model: 'F5-6000J3038F16GX2-TZ5N',
    specs: '32GB (2x16GB) DDR5-6000 CL30',
    currentPrice: 179,
    originalPrice: 219,
    availability: 'In Stock',
    prices: createSamplePrices(179, 219, 'In Stock'),
    priceHistory: createSamplePriceHistory([219, 209, 199, 189, 179]),
    rating: 4.8,
    reviews: 1876,
    image: '/api/placeholder/300/200',
    globalId: 'gskill-trident-z5-32gb'
  },
  // Storage
  {
    id: 'storage-1',
    category: 'storage',
    name: 'Samsung 980 PRO 2TB',
    brand: 'Samsung',
    model: 'MZ-V8P2T0B/AM',
    specs: '2TB NVMe M.2, 7000MB/s Read',
    currentPrice: 149,
    originalPrice: 199,
    availability: 'In Stock',
    prices: createSamplePrices(149, 199, 'In Stock'),
    priceHistory: createSamplePriceHistory([199, 189, 169, 159, 149]),
    rating: 4.9,
    reviews: 4532,
    image: '/api/placeholder/300/200',
    globalId: 'samsung-980-pro-2tb'
  },
  // Motherboards
  {
    id: 'mb-1',
    category: 'motherboard',
    name: 'ASUS ROG Strix X670E-E',
    brand: 'ASUS',
    model: 'ROG STRIX X670E-E GAMING WIFI',
    specs: 'AMD X670E, ATX, WiFi 6E, DDR5',
    currentPrice: 449,
    originalPrice: 529,
    availability: 'In Stock',
    prices: createSamplePrices(449, 529, 'In Stock'),
    priceHistory: createSamplePriceHistory([529, 509, 489, 469, 449]),
    rating: 4.6,
    reviews: 987,
    image: '/api/placeholder/300/200',
    globalId: 'asus-rog-strix-x670e-e'
  },
  // PSU
  {
    id: 'psu-1',
    category: 'psu',
    name: 'Corsair RM850x',
    brand: 'Corsair',
    model: 'CP-9020200-NA',
    specs: '850W, 80+ Gold, Fully Modular',
    currentPrice: 139,
    originalPrice: 169,
    availability: 'In Stock',
    prices: createSamplePrices(139, 169, 'In Stock'),
    priceHistory: createSamplePriceHistory([169, 159, 149, 144, 139]),
    rating: 4.8,
    reviews: 2341,
    image: '/api/placeholder/300/200',
    globalId: 'corsair-rm850x'
  }
];

// Helper functions
export const getProductsByCategory = (categoryId: string): HardwareProduct[] => {
  return hardwareProducts.filter(product => product.category === categoryId);
};

export const getFeaturedProducts = (): HardwareProduct[] => {
  return hardwareProducts.slice(0, 6);
};

export const getProductById = (id: string): HardwareProduct | undefined => {
  return hardwareProducts.find(product => product.id === id);
};

export interface PriceChange {
  amount: number;
  percentage: string;
  isDecrease: boolean;
}

export const calculatePriceChange = (product: HardwareProduct): PriceChange => {
  const change = product.currentPrice - product.originalPrice;
  const percentage = ((change / product.originalPrice) * 100).toFixed(1);
  return {
    amount: change,
    percentage: percentage,
    isDecrease: change < 0
  };
};


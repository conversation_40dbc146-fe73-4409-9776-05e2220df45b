// Types for hardware data
export interface PriceHistory {
  date: string;
  price: number;
}

export interface HardwareProduct {
  id: string;
  category: string;
  name: string;
  brand: string;
  model: string;
  specs: string;
  currentPrice: number;
  originalPrice: number;
  priceHistory: PriceHistory[];
  availability: 'In Stock' | 'Limited Stock' | 'Out of Stock';
  rating: number;
  reviews: number;
  image: string;
}

export interface HardwareCategory {
  id: string;
  name: string;
  icon: string;
  description: string;
}

// Hardware categories data
export const hardwareCategories: HardwareCategory[] = [
  {
    id: 'cpu',
    name: 'Processors (CPU)',
    icon: 'Cpu',
    description: 'Central Processing Units'
  },
  {
    id: 'gpu',
    name: 'Graphics Cards (GPU)',
    icon: 'Monitor',
    description: 'Graphics Processing Units'
  },
  {
    id: 'ram',
    name: 'Memory (RAM)',
    icon: 'HardDrive',
    description: 'Random Access Memory'
  },
  {
    id: 'storage',
    name: 'Storage',
    icon: 'Database',
    description: 'SSDs and HDDs'
  },
  {
    id: 'motherboard',
    name: 'Motherboards',
    icon: 'CircuitBoard',
    description: 'Main circuit boards'
  },
  {
    id: 'psu',
    name: 'Power Supplies',
    icon: 'Zap',
    description: 'Power Supply Units'
  }
];

// Hardware products data
export const hardwareProducts: HardwareProduct[] = [
  // CPUs
  {
    id: 'cpu-1',
    category: 'cpu',
    name: 'AMD Ryzen 9 7950X',
    brand: 'AMD',
    model: '7950X',
    specs: '16 Cores, 32 Threads, 4.5GHz Base',
    currentPrice: 699,
    originalPrice: 799,
    priceHistory: [
      { date: '2024-01-01', price: 799 },
      { date: '2024-02-01', price: 779 },
      { date: '2024-03-01', price: 749 },
      { date: '2024-04-01', price: 729 },
      { date: '2024-05-01', price: 699 }
    ],
    availability: 'In Stock',
    rating: 4.8,
    reviews: 1247,
    image: '/api/placeholder/300/200'
  },
  {
    id: 'cpu-2',
    category: 'cpu',
    name: 'Intel Core i9-14900K',
    brand: 'Intel',
    model: 'i9-14900K',
    specs: '24 Cores, 32 Threads, 3.2GHz Base',
    currentPrice: 589,
    originalPrice: 649,
    priceHistory: [
      { date: '2024-01-01', price: 649 },
      { date: '2024-02-01', price: 629 },
      { date: '2024-03-01', price: 609 },
      { date: '2024-04-01', price: 599 },
      { date: '2024-05-01', price: 589 }
    ],
    availability: 'In Stock',
    rating: 4.7,
    reviews: 892,
    image: '/api/placeholder/300/200'
  },
  // GPUs
  {
    id: 'gpu-1',
    category: 'gpu',
    name: 'NVIDIA RTX 4090',
    brand: 'NVIDIA',
    model: 'RTX 4090',
    specs: '24GB GDDR6X, 2520MHz Boost',
    currentPrice: 1599,
    originalPrice: 1699,
    priceHistory: [
      { date: '2024-01-01', price: 1699 },
      { date: '2024-02-01', price: 1679 },
      { date: '2024-03-01', price: 1649 },
      { date: '2024-04-01', price: 1629 },
      { date: '2024-05-01', price: 1599 }
    ],
    availability: 'Limited Stock',
    rating: 4.9,
    reviews: 2156,
    image: '/api/placeholder/300/200'
  },
  {
    id: 'gpu-2',
    category: 'gpu',
    name: 'AMD RX 7900 XTX',
    brand: 'AMD',
    model: 'RX 7900 XTX',
    specs: '24GB GDDR6, 2500MHz Boost',
    currentPrice: 999,
    originalPrice: 1099,
    priceHistory: [
      { date: '2024-01-01', price: 1099 },
      { date: '2024-02-01', price: 1079 },
      { date: '2024-03-01', price: 1049 },
      { date: '2024-04-01', price: 1029 },
      { date: '2024-05-01', price: 999 }
    ],
    availability: 'In Stock',
    rating: 4.6,
    reviews: 1543,
    image: '/api/placeholder/300/200'
  },
  // RAM
  {
    id: 'ram-1',
    category: 'ram',
    name: 'Corsair Vengeance LPX 32GB',
    brand: 'Corsair',
    model: 'CMK32GX4M2E3200C16',
    specs: '32GB (2x16GB) DDR4-3200 CL16',
    currentPrice: 89,
    originalPrice: 119,
    priceHistory: [
      { date: '2024-01-01', price: 119 },
      { date: '2024-02-01', price: 109 },
      { date: '2024-03-01', price: 99 },
      { date: '2024-04-01', price: 94 },
      { date: '2024-05-01', price: 89 }
    ],
    availability: 'In Stock',
    rating: 4.7,
    reviews: 3421,
    image: '/api/placeholder/300/200'
  },
  {
    id: 'ram-2',
    category: 'ram',
    name: 'G.Skill Trident Z5 32GB',
    brand: 'G.Skill',
    model: 'F5-6000J3038F16GX2-TZ5N',
    specs: '32GB (2x16GB) DDR5-6000 CL30',
    currentPrice: 179,
    originalPrice: 219,
    priceHistory: [
      { date: '2024-01-01', price: 219 },
      { date: '2024-02-01', price: 209 },
      { date: '2024-03-01', price: 199 },
      { date: '2024-04-01', price: 189 },
      { date: '2024-05-01', price: 179 }
    ],
    availability: 'In Stock',
    rating: 4.8,
    reviews: 1876,
    image: '/api/placeholder/300/200'
  },
  // Storage
  {
    id: 'storage-1',
    category: 'storage',
    name: 'Samsung 980 PRO 2TB',
    brand: 'Samsung',
    model: 'MZ-V8P2T0B/AM',
    specs: '2TB NVMe M.2, 7000MB/s Read',
    currentPrice: 149,
    originalPrice: 199,
    priceHistory: [
      { date: '2024-01-01', price: 199 },
      { date: '2024-02-01', price: 189 },
      { date: '2024-03-01', price: 169 },
      { date: '2024-04-01', price: 159 },
      { date: '2024-05-01', price: 149 }
    ],
    availability: 'In Stock',
    rating: 4.9,
    reviews: 4532,
    image: '/api/placeholder/300/200'
  },
  // Motherboards
  {
    id: 'mb-1',
    category: 'motherboard',
    name: 'ASUS ROG Strix X670E-E',
    brand: 'ASUS',
    model: 'ROG STRIX X670E-E GAMING WIFI',
    specs: 'AMD X670E, ATX, WiFi 6E, DDR5',
    currentPrice: 449,
    originalPrice: 529,
    priceHistory: [
      { date: '2024-01-01', price: 529 },
      { date: '2024-02-01', price: 509 },
      { date: '2024-03-01', price: 489 },
      { date: '2024-04-01', price: 469 },
      { date: '2024-05-01', price: 449 }
    ],
    availability: 'In Stock',
    rating: 4.6,
    reviews: 987,
    image: '/api/placeholder/300/200'
  },
  // PSU
  {
    id: 'psu-1',
    category: 'psu',
    name: 'Corsair RM850x',
    brand: 'Corsair',
    model: 'CP-9020200-NA',
    specs: '850W, 80+ Gold, Fully Modular',
    currentPrice: 139,
    originalPrice: 169,
    priceHistory: [
      { date: '2024-01-01', price: 169 },
      { date: '2024-02-01', price: 159 },
      { date: '2024-03-01', price: 149 },
      { date: '2024-04-01', price: 144 },
      { date: '2024-05-01', price: 139 }
    ],
    availability: 'In Stock',
    rating: 4.8,
    reviews: 2341,
    image: '/api/placeholder/300/200'
  }
];

// Helper functions
export const getProductsByCategory = (categoryId: string): HardwareProduct[] => {
  return hardwareProducts.filter(product => product.category === categoryId);
};

export const getFeaturedProducts = (): HardwareProduct[] => {
  return hardwareProducts.slice(0, 6);
};

export const getProductById = (id: string): HardwareProduct | undefined => {
  return hardwareProducts.find(product => product.id === id);
};

export interface PriceChange {
  amount: number;
  percentage: string;
  isDecrease: boolean;
}

export const calculatePriceChange = (product: HardwareProduct): PriceChange => {
  const change = product.currentPrice - product.originalPrice;
  const percentage = ((change / product.originalPrice) * 100).toFixed(1);
  return {
    amount: change,
    percentage: percentage,
    isDecrease: change < 0
  };
};

